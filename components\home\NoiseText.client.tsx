"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import { cn } from "../../lib/utils";

const morphTime = 1.5;

const useMorphingText = (currentText: string, allTexts: string[]) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [displayText, setDisplayText] = useState(currentText);
  const previousTextRef = useRef(currentText);
  const morphRef = useRef(0);
  const timeRef = useRef(new Date());

  const text1Ref = useRef<HTMLSpanElement>(null);
  const text2Ref = useRef<HTMLSpanElement>(null);

  const setStyles = useCallback(
    (fraction: number, oldText: string, newText: string) => {
      const [current1, current2] = [text1Ref.current, text2Ref.current];
      if (!current1 || !current2) return;

      current2.style.filter = `blur(${Math.min(8 / fraction - 8, 100)}px)`;
      current2.style.opacity = `${Math.pow(fraction, 0.4) * 100}%`;

      const invertedFraction = 1 - fraction;
      current1.style.filter = `blur(${Math.min(
        8 / invertedFraction - 8,
        100,
      )}px)`;
      current1.style.opacity = `${Math.pow(invertedFraction, 0.4) * 100}%`;

      current1.textContent = oldText;
      current2.textContent = newText;
    },
    [],
  );

  const resetStyles = useCallback(() => {
    const [current1, current2] = [text1Ref.current, text2Ref.current];
    if (current1 && current2) {
      current1.style.filter = "none";
      current1.style.opacity = "100%";
      current2.style.filter = "none";
      current2.style.opacity = "0%";
      current1.textContent = displayText;
      current2.textContent = "";
    }
  }, [displayText]);

  // Trigger animation when currentText changes
  useEffect(() => {
    if (currentText !== previousTextRef.current && allTexts.includes(currentText)) {
      setIsAnimating(true);
      morphRef.current = 0;
      timeRef.current = new Date();
      previousTextRef.current = currentText;
    }
  }, [currentText, allTexts]);

  useEffect(() => {
    if (!isAnimating) {
      resetStyles();
      return;
    }

    let animationFrameId: number;
    const oldText = displayText;
    const newText = currentText;

    const animate = () => {
      animationFrameId = requestAnimationFrame(animate);

      const newTime = new Date();
      const dt = (newTime.getTime() - timeRef.current.getTime()) / 1000;
      timeRef.current = newTime;

      morphRef.current += dt;
      let fraction = morphRef.current / morphTime;

      if (fraction >= 1) {
        fraction = 1;
        setDisplayText(newText);
        setIsAnimating(false);
      }

      setStyles(fraction, oldText, newText);
    };

    animate();
    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [isAnimating, currentText, displayText, setStyles]);

  return { text1Ref, text2Ref };
};

interface NoiseTextProps {
  currentText: string;
  allTexts: string[];
  className?: string;
}

const SvgFilters: React.FC = () => (
  <svg
    id="filters"
    className="fixed h-0 w-0"
    preserveAspectRatio="xMidYMid slice"
  >
    <defs>
      <filter id="threshold">
        <feColorMatrix
          in="SourceGraphic"
          type="matrix"
          values="1 0 0 0 0
                  0 1 0 0 0
                  0 0 1 0 0
                  0 0 0 255 -140"
        />
      </filter>
    </defs>
  </svg>
);

const NoiseText = ({ currentText, allTexts, className }: NoiseTextProps) => {
  const { text1Ref, text2Ref } = useMorphingText(currentText, allTexts);

  return (
    <div className="noise">
      <div
        className={cn(
          "relative mx-auto w-full text-center [filter:url(#threshold)_blur(0.6px)]",
          className,
        )}
      >
        <span
          className="absolute inset-x-0 top-0 m-auto inline-block w-full"
          ref={text1Ref}
        />
        <span
          className="absolute inset-x-0 top-0 m-auto inline-block w-full"
          ref={text2Ref}
        />
        <SvgFilters />
      </div>
    </div>
  );
};

export default NoiseText;
